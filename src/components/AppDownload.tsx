import { Card } from "./Card";
import { useEffect, useState } from "react";
import dog from "../assets/svg/dog.svg";
import cat from "../assets/svg/cat.svg";
import chef from "../assets/svg/chef.svg";
import meat from "../assets/svg/meat.svg";
import grill from "../assets/svg/grill.svg";
import fruit from "../assets/svg/fruit.svg";
import bread from "../assets/svg/bread.svg";
import muscle from "../assets/svg/muscle.svg";
import parasol from "../assets/svg/parasol.svg";
import appleIcon from "../assets/svg/apple_icon.svg";
import listTypes from "../assets/svg/list-types.svg";
import basketFruit from "../assets/svg/basketFruit.svg";
import ShoppingCart from "../assets/svg/shopingCart.svg";
import googleStoreIcon from "../assets/svg/google_store_icon.svg";

export function AppDownloadSection() {

  const [currentIcon, setCurrentIcon] = useState(listTypes);

  useEffect(() => {
    const cards = document.querySelectorAll(".animar-card");

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const el = entry.target as HTMLElement;
          if (entry.isIntersecting) {
            el.style.opacity = "1";
            el.style.transform = "translateY(0)";
            el.style.transition = "opacity 0.6s ease, transform 0.6s ease";

          }
        });
      },
      { threshold: 0.2 }
    );

    cards.forEach((el) => {
      const elem = el as HTMLElement;
      elem.style.opacity = "0";
      elem.style.transform = "translateY(30px)";
      observer.observe(elem);
    });

    // Troca de ícones
    const icons = [listTypes, basketFruit, fruit, bread, meat, muscle, dog, cat, grill, chef, parasol];
    let index = 0;
    const interval = setInterval(() => {
      index = (index + 1) % icons.length;
      setCurrentIcon(icons[index]);
    }, 1000);
    return () => {
      observer.disconnect();
      clearInterval(interval);
    };
  }, []);

  return (
    <section className="w-full bg-white px-4 lg:px-16 py-16 lg:pt-36">
      <div className="max-w-6xl mx-auto flex flex-col justify-center items-center gap-8">
      
        <div className=" w-full flex flex-col gap-6 lg:flex-row lg:gap-32 lg:items-center lg:justify-between">
          <div className="flex flex-col gap-4">
            <img src={ShoppingCart} alt="Logo Izy" className="w-[42px] h-[42px]" />
            <h2 className="text-[32px] lg:text-3xl font-extralight text-dark_warm_gray">
              Receba seus produtos fresquinhos, baixe nosso app!
            </h2>
          </div>

          <div className="flex gap-4 w-full md:justify-center lg:justify-end">
            <a
              href="#"
              className="flex-1 gap-[40px] p-4 border rounded-lg shadow-sm text-center flex flex-col items-start text-start md:max-w-[148px] hover:shadow-lg "
            >
              <img src={appleIcon} alt="Apple Store" className="w-8 h-8" />
              <p className="text-xs text-slate_gray font-light">
                Disponível na<br />
                <strong className="text-dark_warm_gray text-base">Apple Store</strong>
              </p>
            </a>
            <a
              href="#"
              className="flex-1 gap-[40px] p-4 border rounded-lg shadow-sm text-center flex flex-col items-start text-start md:max-w-[148px] hover:shadow-lg"
            >
              <img src={googleStoreIcon} alt="Google Play" className="w-8 h-8" />
              <p className="text-xs text-slate_gray font-light">
                Disponível na<br />
                <strong className="text-dark_warm_gray text-base">Google Play</strong>
              </p>
            </a>
          </div>
        </div>

       
        <div className="flex flex-col items-center gap-4 mt-8 order-1 lg:order-2 lg:mt-[100px]">
          <img src={currentIcon} alt="Izy App" className="w-[56px] h-[55px] transition-all duration-500 ease-in-out" />
          <p className="text-xl text-center text-slate_gray font-thin lg:text-4xl">
            Seu mercado, <span className="font-medium">para todos os momentos!</span>
          </p>
        </div>

        <div className="w-full  overflow-y-hidden overflow-x-auto scrollbar-none order-2 lg:order-1">
          <div className="flex gap-4 min-w-max lg:min-w-0 lg:grid lg:grid-cols-5 lg:gap-4">
            <div className="animar-card"><Card icon={basketFruit} label="Izy Mercado" /></div>
            <div className="animar-card"><Card icon={fruit} label="Izy Hortifruti" /></div>
            <div className="animar-card"><Card icon={bread} label="Izy Padaria" /></div>
            <div className="animar-card"><Card icon={meat} label="Izy Carnes" /></div>
            <div className="animar-card"><Card icon={muscle} label="Izy Fit" /></div>
          </div>
        </div>
      </div>
    </section>
  );
}

