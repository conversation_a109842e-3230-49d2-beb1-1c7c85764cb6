import { useState } from "react";
import heartColor from '../assets/svg/heart-colors.svg'
import basketFruit from '../assets/svg/basketFruit.svg'
import { useSwipeable } from 'react-swipeable';

const testimonials = [
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "<PERSON><PERSON><PERSON>",
    text: "Com a <PERSON>, não preciso mais ficar horas em um supermercado esperando em uma fila, recebo meus produtos no conforto de minha casa!",

  },
  {
    name: "<PERSON>",
    role: "<PERSON>lient<PERSON>",
    text: "Já economizei bastante com o cashback e o atendimento é excelente!",

  },
  {
    name: "<PERSON>",
    role: "<PERSON><PERSON><PERSON>",
    text: "<PERSON><PERSON> fácil de usar, entrega no prazo e preços justos!",

  },
];
export function Testimonials() {

  const handlers = useSwipeable({
    onSwipedLeft: () => {
      if (currentIndex < testimonials.length - 1) {
        nextTestimonial();
      }
    },
    onSwipedRight: () => {
      if (currentIndex > 0) {
        prevTestimonial();
      }
    },
    trackTouch: true,
    trackMouse: false,
  });



  const [currentIndex, setCurrentIndex] = useState(0);

  const prevTestimonial = () => {
    setCurrentIndex((prev) =>
      prev === 0 ? testimonials.length - 1 : prev - 1
    );
  };

  const nextTestimonial = () => {
    setCurrentIndex((prev) =>
      prev === testimonials.length - 1 ? 0 : prev + 1
    );
  };

  return (
    <section className="w-full pt-72 lg:pt-[160px] px-4 bg-white flex flex-col items-center gap-12 lg:flex-row lg:justify-center lg:gap-24">

      <div className="flex flex-col items-start gap-8 w-full max-w-sm">
        <div className="flex flex-col items-start gap-4">
          <img src={heartColor} alt="" className="w-[42px]" />
          <h2 className="text-4xl font-extralight text-dark_warm_gray text-left">
            O que falam da Izy?
          </h2>
        </div>

        <button className="bg-gradient-pink-purple text-white px-8 py-4 rounded-full text-sm w-full lg:max-w-[309px] hover:bg-[linear-gradient(90deg,_#9B5BC2_0%,_#C67474_100%)]">
          Quero fazer minhas compras com a Izy
        </button>
      </div>


      <div className="flex flex-col items-center w-full max-w-md">
        <div  {...handlers} className="w-full p-6 rounded-xl border border-gray-200 shadow-sm bg-white relative flex flex-col justify-between h-[300px]">

          <img
            src={basketFruit}
            alt="Ícone"
            className="w-[56px] h-[56px] mb-4"
          />

          <div className="flex flex-col gap-8">

            <p className="text-slate_gray text-base font-light leading-[1.2rem]">
              {
                testimonials[currentIndex].text
              }
            </p>

            <div className="mt-6">
              <p className="text-xs font-normal text-dark_warm_gray">
                {testimonials[currentIndex].name}
              </p>
              <p className="text-xs font-light text-slate_gray">
                {testimonials[currentIndex].role}
              </p>
            </div>
          </div>
        </div>


        <div className="mt-4 flex items-center justify-between w-full px-2">
          <div className="flex gap-1">
            {testimonials.map((_, i) => (
              <span
                key={i}
                className={`transition-all ${i === currentIndex
                  ? "w-4 h-1 rounded-full bg-gradient-pink-purple"
                  : "w-1 h-1 rounded-full bg-gray-200"
                  }`}
              ></span>
            ))}
          </div>

          <div className="flex gap-2">

            <button
              onClick={prevTestimonial}
              disabled={currentIndex === 0}
              className={`w-8 h-8 flex items-center justify-center border border-pale_gray rounded-full transition
        ${currentIndex === 0 ? "cursor-not-allowed" : "hover:bg-gray-100"}`}
            >
              <span className="sr-only">Anterior</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-4 w-4 ${currentIndex === 0 ? "text-gray-300" : "text-gray-500"}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>


            <button
              onClick={nextTestimonial}
              disabled={currentIndex === testimonials.length - 1}
              className={`w-8 h-8 flex items-center justify-center border border-pale_gray rounded-full transition
        ${currentIndex === testimonials.length - 1 ? "cursor-not-allowed" : "hover:bg-gray-100"}`}
            >
              <span className="sr-only">Próximo</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-4 w-4 ${currentIndex === testimonials.length - 1 ? "text-gray-300" : "text-gray-500"}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>


      </div>
    </section>
  );
}



