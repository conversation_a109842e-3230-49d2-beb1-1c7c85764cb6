import starLight from "../assets/svg/star-light.svg";
import ShoppingCart from "@/assets/svg/shopingCart.svg";
import secondBannerImage from "../assets/secondBanner.png"
import bigSecondImage from "../assets/bigSecondBanner.png"
import { BannerImages } from './BannerImages'
import { MobileCard } from "./MobileCard";
import { PartnerText } from "./PartnerText";
import { StaticStoreCard } from './StaticStoreCard'
import { SideCard } from "./SideCardPartnerSection";


export function PartnerSection() {
  if (typeof window !== "undefined") {
    setTimeout(() => {
      const elements = document.querySelectorAll(".fade-up");
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.classList.add("animate");
              observer.unobserve(entry.target);
            }
          });
        },
        { threshold: 0.3 }
      );

      elements.forEach((el) => observer.observe(el));
    }, 100);
  }

  return (
    <section className="bg-gradient-to-br from-background to-accent/20">
      <style>{`
        @keyframes pulseScale {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }

        @keyframes fadeUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .fade-up {
          opacity: 0;
          transform: translateY(30px);
          transition: opacity 1s ease-out, transform 1s ease-out;
        }

        .fade-up.animate {
          animation: fadeUp 1s ease forwards;
        }
      `}</style>

      <div className="container mx-auto flex flex-col items-center justify-center gap-8 px-4 sm:py-20 lg:flex-row lg:gap-28 lg:justify-center lg:py-36 lg:px-20">
        <PartnerText />

        <div className="relative mx-auto max-w-[600px] w-full flex flex-col items-center">
          <BannerImages mobileSrc={secondBannerImage} desktopSrc={bigSecondImage} alt="banner" />

          <StaticStoreCard />

          <div className="hidden lg:block">
            <SideCard
              icon={ShoppingCart}
              title="Mais visibilidade para o seu negócio"
              description="Seja parceiro da Izy e comece a vender mais, com nossa tecnologia"
              positionClass="bottom-[70%] left-[-13%]"
            />
            <SideCard
              icon={starLight}
              iconBgClass="bg-primary/20"
              title="Relatórios e dados de performance"
              description="Com a Izy, você sabe exatamente o que vender e por quanto irá vender"
              positionClass="bottom-[48%] right-[-12%]"
            />
          </div>

          <div className="lg:hidden w-full ">
            <div className="w-full mt-4 flex justify-between md:justify-center  gap-4 ">
              <MobileCard
                icon={ShoppingCart}
                title="Mais visibilidade para o seu negócio"
                description="Seja parceiro da Izy e comece a vender mais, com nossa tecnologia"
              />
              <MobileCard
                icon={starLight}
                iconBgClass="bg-primary/20"
                title="Relatórios e dados de performance"
                description="Com a Izy, você sabe exatamente o que vender e por quanto irá vender"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
