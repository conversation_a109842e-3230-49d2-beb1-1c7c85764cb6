export function MobileCard({
  icon,
  iconBgClass = "",
  title,
  description,
}: {
  icon: string;
  iconBgClass?: string;
  title: string;
  description: string;
}) {
  return (
    <div className="rounded-xl bg-white p-4 shadow-lg backdrop-blur-sm  fade-up h-full max-[390px]:p-3 ">
      <div className="flex h-full justify-between flex-col gap-8 max-[430px]:gap-4 ">
        <div
          className={`flex items-center justify-center h-[42px] w-[42px] rounded-full ${iconBgClass} max-[430px]:h-[32px] max-[430px]:w-[32px]`}
          style={{ animation: "pulseScale 3s ease-in-out infinite" }}
        >
          <img src={icon} alt="" className="h-full w-full" />
        </div>
        <div className="flex flex-col gap-2 max-[430px]:gap-1">
          <p className="text-sm text-transparent bg-clip-text bg-gradient-pink-purple leading-tight  max-[420px]:text-xs">
            {title}
          </p>
          <p className="text-xs  font-light leading-light text-slate_gray max-[340px]:leading-[10px] max-[420px]:text-[10px]">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}